﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
    <AssemblyName>DTXMania.Test.Mac</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="Moq" Version="4.20.70" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DTXMania.Game\DTXMania.Game.Mac.csproj" />
  </ItemGroup>

  <!-- Include all source files except UI tests and graphics-dependent tests -->
  <ItemGroup>
    <Compile Include="**/*.cs" Exclude="UI/**/*.cs;Helpers/TestGraphicsDeviceService.cs;Helpers/MockResourceManager.cs;Resources/BitmapFontTests.cs" />
  </ItemGroup>

  <!-- Include xunit configuration if it exists -->
  <ItemGroup>
    <None Include="xunit.runner.json" Condition="Exists('xunit.runner.json')">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>