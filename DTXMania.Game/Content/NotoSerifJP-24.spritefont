<?xml version="1.0" encoding="utf-8"?>
<!--
This file contains an xml description of a font, and will be read by the XNA
Framework Content Pipeline. Follow the comments to customize the appearance
of the font in your game, and to change the characters which are available to draw
with.
-->
<XnaContent xmlns:Graphics="Microsoft.Xna.Framework.Content.Pipeline.Graphics">
  <Asset Type="Graphics:FontDescription">    <!--
    Modify this string to change the font that will be imported.
    -->
    <FontName>NotoSerifJP</FontName>

    <!--
    Size is a float value, measured in points. Modify this value to change
    the size of the font.
    -->
    <Size>24</Size>

    <!--
    Spacing is a float value, measured in pixels. Modify this value to change
    the amount of spacing in between characters.
    -->
    <Spacing>0</Spacing>

    <!--
    UseKerning controls the layout of the font. If this value is true, kerning information
    will be used when placing characters.
    -->
    <UseKerning>true</UseKerning>

    <!--
    Style controls the style of the font. Valid entries are "Regular", "Bold", "Italic",
    and "Bold, Italic", and are case sensitive.
    -->
    <Style>Regular</Style>

    <!--
    If you uncomment this line, the default character will be substituted if you draw
    or measure text that contains characters which were not included in the font.
    -->
    <DefaultCharacter>*</DefaultCharacter>

    <!--
    CharacterRegions control what letters are available in the font. Every
    character from Start to End will be built and made available for drawing. The
    default range is from 32, (ASCII space), to 126, (ASCII tilde), covering the
    basic Latin character set. The characters are ordered according to the Unicode
    standard.
    See the documentation for more information.
    -->    <CharacterRegions>
      <CharacterRegion>
        <Start>&#32;</Start>
        <End>&#126;</End>
      </CharacterRegion>
      <!-- Japanese Hiragana -->
      <CharacterRegion>
        <Start>&#12352;</Start>
        <End>&#12447;</End>
      </CharacterRegion>
      <!-- Japanese Katakana -->
      <CharacterRegion>
        <Start>&#12448;</Start>
        <End>&#12543;</End>
      </CharacterRegion>
      <!-- Japanese punctuation and symbols -->
      <CharacterRegion>
        <Start>&#12289;</Start>
        <End>&#12351;</End>
      </CharacterRegion>
      <!-- CJK Unified Ideographs (Complete range for common Kanji) -->
      <CharacterRegion>
        <Start>&#19968;</Start>
        <End>&#40959;</End>
      </CharacterRegion>
      <!-- CJK Compatibility Ideographs -->
      <CharacterRegion>
        <Start>&#63744;</Start>
        <End>&#64255;</End>
      </CharacterRegion>
      <!-- Additional Japanese symbols -->
      <CharacterRegion>
        <Start>&#65280;</Start>
        <End>&#65519;</End>
      </CharacterRegion>
    </CharacterRegions>
  </Asset>
</XnaContent>