# DTXManiaCX Product Overview

DTXManiaCX is a rhythm game engine - a complete rewrite of DTXMania using modern .NET 8 and MonoGame framework. It's designed to play DTX format music files, similar to popular rhythm games like Guitar Hero or Rock Band.

## Key Features
- Cross-platform support (Windows, macOS)
- Stage-based architecture with smooth transitions
- Configurable graphics settings and input handling
- Resource management system for textures, sounds, and fonts
- Song database and selection system
- Comprehensive UI framework with layout management

## Target Platforms
- Windows (DirectX backend)
- macOS (OpenGL backend)

The project follows a modular architecture with clear separation between game logic, graphics management, input handling, and resource management.